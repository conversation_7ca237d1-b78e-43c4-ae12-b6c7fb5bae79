import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { useOrderList } from '../../services/useOrderList';

export const BackorderedFilter = () => {
  const { total, queryParams } = useOrderList();
  const isActive = queryParams.backordered === 'true';

  const handleClick = () => {
    queryParams.setBackordered(isActive ? 'false' : 'true');
  };

  return (
    <Button
      onClick={handleClick}
      variant="white"
      className={`w-auto ${isActive ? 'bg-[#344054] text-white' : ''}`}
    >
      <Icon name="backorder" color={isActive ? '#FFFFFF' : '#000000'} />{' '}
      <span className="ml-2">Backorders</span>
      {isActive && <span className="ml-2">{`(${total})`}</span>}
    </Button>
  );
};
