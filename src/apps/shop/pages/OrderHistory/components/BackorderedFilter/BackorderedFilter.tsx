import { Icon } from '@/libs/icons/Icon';
import { Button } from '@/libs/ui/Button/Button';
import { useOrderList } from '../../services/useOrderList';
import { useState } from 'react';

export const BackorderedFilter = () => {
  const [active, setActive] = useState(false);
  const { total, queryParams } = useOrderList();

  const handleClick = () => {
    queryParams.setBackordered(active ? 'false' : 'true');
    setActive(!active);
  };

  return (
    <Button
      onClick={handleClick}
      variant="white"
      className={`w-auto ${active ? 'bg-[#344054] text-white' : ''}`}
    >
      <Icon name="backorder" color={active ? '#FFFFFF' : '#000000'} />{' '}
      <span className="ml-2">Backorders</span>
      {active && <span className="ml-2">{`(${total})`}</span>}
    </Button>
  );
};
