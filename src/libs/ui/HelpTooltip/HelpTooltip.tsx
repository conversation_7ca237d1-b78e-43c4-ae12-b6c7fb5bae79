import { Tooltip } from '@/libs/ui/Tooltip';
import { Button } from '@/libs/ui/Button/Button';
import QuestionIcon from './assets/question.svg?react';

interface HelpTooltipProps {
  message: string;
}

export const HelpTooltip = ({ message }: HelpTooltipProps) => (
  <Tooltip label={message} side="right" align="start">
    <Button variant="unstyled" type="button">
      <QuestionIcon />
    </Button>
  </Tooltip>
);
